'use client';

import { SupabaseClient, User } from '@supabase/supabase-js';

/**
 * @name PopupAuthResult
 * @description Defines the return type for popup authentication methods.
 */
export interface PopupAuthResult {
  success: boolean;
  error?: string;
  user?: User;
}

/**
 * @name PopupAuthService
 * @description Handles popup-based OAuth authentication with proper PKCE flow.
 * This service is designed to work in the browser environment where PKCE state is available.
 */
export class PopupAuthService {
  /**
   * @param {SupabaseClient} client - Supabase browser client instance.
   */
  constructor(private readonly client: SupabaseClient) {}

  /**
   * @name exchangeCodeForSession
   * @description Exchanges an authorization code for a session using client-side PKCE flow.
   * This method should be called from the main window after receiving the code from the popup.
   *
   * @param {string} authCode - The authorization code from OAuth callback.
   * @param {string} [anonymousUserId] - Optional anonymous user ID for document transfer.
   * @returns {Promise<PopupAuthResult>} - The result of the auth exchange.
   */
  async exchangeCodeForSession(
    authCode: string,
    anonymousUserId?: string,
  ): Promise<PopupAuthResult> {
    try {
      // Exchange code for session using client-side PKCE flow
      const { data: exchangeData, error: exchangeError } = await this.client.auth.exchangeCodeForSession(authCode);

      if (exchangeError) {
        console.error('Error exchanging code for session in popup flow:', {
          error: exchangeError,
          context: 'popup.auth.callback',
        });
        return {
          success: false,
          error: exchangeError.message,
        };
      }

      const authenticatedUser = exchangeData?.user;
      const authenticatedUserId = authenticatedUser?.id;

      // Transfer document ownership if both users exist and are different
      if (anonymousUserId && authenticatedUserId && anonymousUserId !== authenticatedUserId) {
        try {
          await this.transferDocumentOwnership(anonymousUserId, authenticatedUserId);
          console.info('Document ownership transfer completed successfully in popup flow', {
            fromUserId: anonymousUserId,
            toUserId: authenticatedUserId,
            context: 'popup.auth.callback',
          });
        } catch (transferError) {
          console.error('Error during document ownership transfer in popup flow:', {
            error: transferError,
            fromUserId: anonymousUserId,
            toUserId: authenticatedUserId,
            context: 'popup.auth.callback',
          });
          // Don't fail the entire authentication process for transfer errors
        }
      }

      return {
        success: true,
        user: authenticatedUser,
      };
    } catch (error) {
      console.error('Unexpected error during popup authentication callback:', {
        error,
        context: 'popup.auth.callback',
      });
      return {
        success: false,
        error: 'Unexpected error during authentication',
      };
    }
  }

  /**
   * @name transferDocumentOwnership
   * @description Transfers document ownership from anonymous user to authenticated user.
   * This is a simplified version that makes a request to the server to handle the transfer.
   * @param {string} fromUserId - The anonymous user ID.
   * @param {string} toUserId - The authenticated user ID.
   * @returns {Promise<void>} - Resolves when transfer is complete.
   * @throws {Error} - Throws error if transfer fails.
   * @private
   */
  private async transferDocumentOwnership(fromUserId: string, toUserId: string): Promise<void> {
    try {
      // Make a request to the server to handle document transfer
      const response = await fetch('/api/auth/transfer-documents', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          fromUserId,
          toUserId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to transfer documents');
      }

      console.info('Document ownership transfer request sent successfully', {
        fromUserId,
        toUserId,
        context: 'popup.document.transfer',
      });
    } catch (error) {
      console.error('Document ownership transfer failed:', {
        error,
        fromUserId,
        toUserId,
        context: 'popup.document.transfer',
      });
      throw error;
    }
  }
}

/**
 * @name createPopupAuthService
 * @description Factory function to create an instance of `PopupAuthService`.
 * @param {SupabaseClient} client - Supabase browser client instance.
 * @returns {PopupAuthService} - New instance of `PopupAuthService`.
 */
export function createPopupAuthService(client: SupabaseClient): PopupAuthService {
  return new PopupAuthService(client);
}
